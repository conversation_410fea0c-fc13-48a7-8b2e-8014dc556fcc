import React, { useState, memo, useCallback } from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import QRCodeSection from "./QRCodeSection";
import { useLanguage } from "../../contexts/LanguageContext";
import { getTranslation } from "../../utils/translations";

interface AccessDeniedScreenProps {
	onCancel: () => void;
	title?: string;
	subtitle?: string;
}

/**
 * AccessDeniedScreen Component
 * Full-screen component shown when user doesn't have access to paid content
 * Displays QR code for subscription management with a cancel button
 * Designed for TV navigation with auto-focus support
 * Optimized with React.memo for performance
 */
const AccessDeniedScreen: React.FC<AccessDeniedScreenProps> = memo(
	({ onCancel, title, subtitle }) => {
		const [isFocused, setIsFocused] = useState(false);

		// Get current language for translations
		const { currentLanguage } = useLanguage();

		// Use translations with fallback to props or default values
		const displayTitle =
			title || getTranslation("accessDeniedTitle", currentLanguage);
		const displaySubtitle =
			subtitle ||
			getTranslation("accessDeniedSubtitle", currentLanguage);

		// Memoized focus handlers for performance
		const handleFocus = useCallback(() => setIsFocused(true), []);
		const handleBlur = useCallback(() => setIsFocused(false), []);

		return (
			<View
				style={styles.container}
				importantForAccessibility="no-hide-descendants"
			>
				<View style={styles.content}>
					{/* Combined title text - matching the image layout */}
					<Text style={styles.mainTitle}>
						{displayTitle}
						{"\n"}
						{displaySubtitle}
					</Text>

					{/* QR Code Section - no title since it's included above */}
					<QRCodeSection
						showTitle={false}
						style={styles.qrSection}
					/>

					{/* Cancel Button */}
					<Pressable
						style={[
							styles.cancelButton,
							isFocused && styles.cancelButtonFocused,
						]}
						onPress={onCancel}
						onFocus={handleFocus}
						onBlur={handleBlur}
						hasTVPreferredFocus={true} // Auto-focus on TV platforms
						focusable={true}
						accessible={true}
						accessibilityRole="button"
						accessibilityLabel="Cancel and go back"
					>
						<Text
							style={[
								styles.cancelButtonText,
								isFocused && styles.cancelButtonTextFocused,
							]}
						>
							{getTranslation("cancel", currentLanguage)}
						</Text>
					</Pressable>
				</View>
			</View>
		);
	}
);

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	content: {
		alignItems: "center",
		justifyContent: "center",
		paddingHorizontal: scale(64),
		maxWidth: scale(1400),
		width: "100%",
	},
	mainTitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(52),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(80),
		letterSpacing: scale(3),
		lineHeight: scale(68),
		maxWidth: scale(1200),
	},
	qrSection: {
		marginBottom: scale(100),
	},
	cancelButton: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: scale(8),
		paddingVertical: scale(32),
		paddingHorizontal: scale(120),
		minWidth: scale(600),
		alignItems: "center",
		justifyContent: "center",
		borderWidth: scale(4),
		borderColor: "transparent",
		shadowColor: "#000",
		shadowOffset: { width: 0, height: scale(4) },
		shadowOpacity: 0.3,
		shadowRadius: scale(8),
		elevation: 8,
	},
	cancelButtonFocused: {
		backgroundColor: "#cc0000", // Darker red when focused
		borderColor: "#ffffff", // White border when focused
		transform: [{ scale: 1.05 }], // Slightly larger when focused
		shadowOpacity: 0.5,
	},
	cancelButtonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(42),
		fontWeight: "bold",
		letterSpacing: scale(3),
	},
	cancelButtonTextFocused: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
	},
});

// Set display name for debugging
AccessDeniedScreen.displayName = "AccessDeniedScreen";

export default AccessDeniedScreen;
