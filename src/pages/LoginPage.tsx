import React, { useState, memo, useCallback } from "react";
import {
	View,
	Text,
	TextInput,
	StyleSheet,
	ActivityIndicator,
	Platform,
	Pressable,
} from "react-native";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import {
	AuthService,
	LoginCredentials,
} from "../services/authService";
import { QRCodeSection } from "../components/common";
import { useLanguage } from "../contexts/LanguageContext";
import { getTranslation } from "../utils/translations";

/**
 * LoginPage Component
 * Displays login form alongside QR code for account management
 * Optimized with React.memo for performance
 * Enhanced with TV-specific focus management and keyboard handling
 */
const LoginPage = memo(() => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [currentFocus, setCurrentFocus] = useState<
		"email" | "password" | "button" | null
	>(null);
	const [showKeyboard, setShowKeyboard] = useState(false);
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Get current language for translations
	const { currentLanguage } = useLanguage();

	// References for input fields to manage focus
	const emailInputRef = React.useRef<TextInput>(null);
	const passwordInputRef = React.useRef<TextInput>(null);

	// Memoized handlers for performance
	const handleEmailChange = useCallback(
		(text: string) => {
			setEmail(text);
			if (error) setError("");
		},
		[error]
	);

	const handlePasswordChange = useCallback(
		(text: string) => {
			setPassword(text);
			if (error) setError("");
		},
		[error]
	);

	// Handle input field selection
	const handleInputSelect = useCallback(
		(inputType: "email" | "password") => {
			if (Platform.isTV) {
				setShowKeyboard(true);
				if (inputType === "email") {
					emailInputRef.current?.focus();
				} else {
					passwordInputRef.current?.focus();
				}
			}
		},
		[]
	);

	/**
	 * Handle login form submission
	 * Validates input and calls authentication service
	 */
	const handleLogin = async () => {
		setError("");

		if (!email.trim()) {
			setError(getTranslation("emailRequired", currentLanguage));
			return;
		}

		if (!AuthService.validateEmail(email)) {
			setError(getTranslation("validEmailRequired", currentLanguage));
			return;
		}

		if (!password.trim()) {
			setError(getTranslation("passwordRequired", currentLanguage));
			return;
		}

		if (!AuthService.validatePassword(password)) {
			setError(getTranslation("passwordMinLength", currentLanguage));
			return;
		}

		setIsLoading(true);

		try {
			console.log("🔐 Starting login attempt for:", email);
			const credentials: LoginCredentials = {
				email: email.trim(),
				password: password,
			};
			const result = await AuthService.login(credentials);

			if (result.success) {
				console.log(
					"✅ Login successful! Navigating back to HomePage"
				);
				console.log(
					"🏠 HomePage will re-check authentication status on focus"
				);
				navigation.goBack();
			} else {
				console.log("❌ Login failed:", result.error);
				setError(
					result.error ||
						getTranslation("loginFailed", currentLanguage)
				);
			}
		} catch (error) {
			console.log("💥 Unexpected login error:", error);
			setError(getTranslation("unexpectedError", currentLanguage));
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<View style={styles.pageBackground}>
			<View style={styles.mainContainer}>
				{/* Left side - Login Form */}
				<View style={styles.loginSection}>
					<View style={styles.card}>
						<Text style={styles.title}>
							{getTranslation("login", currentLanguage)}
						</Text>

						{/* Error message display */}
						{error ? (
							<View style={styles.errorContainer}>
								<Text style={styles.errorText}>{error}</Text>
							</View>
						) : null}

						<View style={styles.formGroup}>
							<Text style={styles.label}>
								{getTranslation("email", currentLanguage)}
							</Text>
							<Pressable
								style={[
									styles.inputWrapper,
									currentFocus === "email" &&
										styles.inputWrapperFocused,
								]}
								onPress={() => handleInputSelect("email")}
								onFocus={() => setCurrentFocus("email")}
								onBlur={() => {
									if (!showKeyboard) {
										emailInputRef.current?.blur();
									}
								}}
								focusable={true}
								hasTVPreferredFocus={true}
							>
								<TextInput
									ref={emailInputRef}
									style={[
										styles.input,
										error ? styles.inputError : null,
									]}
									value={email}
									onChangeText={handleEmailChange}
									placeholder={getTranslation(
										"enterEmail",
										currentLanguage
									)}
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									autoCapitalize="none"
									keyboardType="email-address"
									editable={!isLoading}
									accessible={true}
									accessibilityLabel="Email input field"
									accessibilityRole="keyboardkey"
									onBlur={() => {
										if (!showKeyboard) {
											setShowKeyboard(false);
										}
									}}
								/>
							</Pressable>
						</View>

						<View style={styles.formGroup}>
							<Text style={styles.label}>
								{getTranslation("password", currentLanguage)}
							</Text>
							<Pressable
								style={[
									styles.inputWrapper,
									currentFocus === "password" &&
										styles.inputWrapperFocused,
								]}
								onPress={() => handleInputSelect("password")}
								onFocus={() => setCurrentFocus("password")}
								onBlur={() => {
									if (!showKeyboard) {
										passwordInputRef.current?.blur();
									}
								}}
								focusable={true}
							>
								<TextInput
									ref={passwordInputRef}
									style={[
										styles.input,
										error ? styles.inputError : null,
									]}
									value={password}
									onChangeText={handlePasswordChange}
									placeholder={getTranslation(
										"enterPassword",
										currentLanguage
									)}
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									secureTextEntry
									editable={!isLoading}
									accessible={true}
									accessibilityLabel="Password input field"
									accessibilityRole="keyboardkey"
									onBlur={() => {
										if (!showKeyboard) {
											setShowKeyboard(false);
										}
									}}
								/>
							</Pressable>
						</View>

						<Pressable
							style={({ pressed }) => [
								styles.button,
								{ opacity: isLoading ? 0.5 : 0.7 },
								isLoading && styles.buttonDisabled,
								(pressed || currentFocus === "button") &&
									styles.buttonFocused,
							]}
							onPress={handleLogin}
							disabled={isLoading}
							onFocus={() => setCurrentFocus("button")}
							onBlur={() => {
								if (!isLoading) {
									setCurrentFocus(null);
								}
							}}
							focusable={true}
							accessible={true}
							accessibilityRole="button"
							accessibilityLabel="Log in button"
						>
							{isLoading ? (
								<View style={styles.buttonContent}>
									<ActivityIndicator
										size="small"
										color={GLOBAL_STYLES.COLORS.TEXT_PRIMARY}
										style={styles.loadingIndicator}
									/>
									<Text style={styles.buttonText}>
										{getTranslation("loggingIn", currentLanguage)}
									</Text>
								</View>
							) : (
								<Text style={styles.buttonText}>
									{getTranslation("logIn", currentLanguage)}
								</Text>
							)}
						</Pressable>
					</View>
				</View>

				{/* Right side - QR Code Section */}
				<View style={styles.qrSection}>
					<QRCodeSection
						title={getTranslation("manageAccountQR", currentLanguage)}
						showTitle={true}
					/>
				</View>
			</View>
		</View>
	);
});

const styles = StyleSheet.create({
	pageBackground: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	mainContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
		maxWidth: scale(1600),
		paddingHorizontal: scale(64),
	},
	loginSection: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		paddingRight: scale(80),
	},
	qrSection: {
		flex: 1,
		alignItems: "flex-start",
		justifyContent: "center",
		paddingLeft: scale(80),
	},
	card: {
		backgroundColor: "#131c2b",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(80),
		minWidth: scale(700),
		width: "100%",
		maxWidth: scale(800),
		alignItems: "center",
		shadowOpacity: 0.2,
		shadowRadius: scale(8),
		elevation: scale(4),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(64),
		letterSpacing: scale(2),
	},
	formGroup: {
		width: "100%",
		marginBottom: scale(48),
	},
	label: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(32),
		marginBottom: scale(16),
		marginLeft: scale(2),
	},
	inputWrapper: {
		width: "100%",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		borderWidth: scale(2),
		borderColor: "#22304a",
		backgroundColor: "#18243a",
	},
	inputWrapperFocused: {
		borderColor: "#ffffff",
		borderWidth: scale(3),
		transform: [{ scale: 1.02 }],
	},
	input: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		paddingVertical: scale(28),
		paddingHorizontal: scale(32),
		fontSize: scale(32),
		width: "100%",
		backgroundColor: "transparent",
	},
	button: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: 0,
		marginTop: scale(32),
		width: "100%",
		alignItems: "center",
	},
	buttonFocused: {
		opacity: 1,
		transform: [{ scale: 1.02 }],
		borderWidth: scale(3),
		borderColor: "#ffffff",
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		letterSpacing: scale(1),
	},
	buttonDisabled: {
		opacity: 0.5,
	},
	buttonContent: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
	},
	loadingIndicator: {
		marginRight: scale(12),
	},
	errorContainer: {
		backgroundColor: "#ff4444",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(16),
		marginBottom: scale(24),
		width: "100%",
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		textAlign: "center",
	},
	inputError: {
		borderColor: "#ff4444",
	},
});

// Set display name for debugging
LoginPage.displayName = "LoginPage";

export default LoginPage;
